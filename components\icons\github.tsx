export const GithubIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 16 16"
    fill="currentColor"
    role="img"
    focusable="false"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.5127 1.96247C10.4401 1.32066 9.27013 0.999756 7.99946 0.999756C6.72987 0.999756 5.5588 1.32066 4.48573 1.96247C3.41428 2.60373 2.56465 3.4742 1.939 4.57333C1.31228 5.67358 1 6.87335 1 8.17542C1 9.73858 1.44527 11.1441 2.33528 12.393C3.22529 13.6398 4.37482 14.5063 5.78494 14.9843C5.94862 15.0156 6.0703 14.9938 6.14891 14.9189C6.22806 14.8435 6.26736 14.7512 6.26736 14.6394L6.26306 14.1334C6.25983 13.8159 6.25767 13.5391 6.25767 13.3021L6.04877 13.339C5.91524 13.3642 5.74617 13.3748 5.54265 13.3714C5.33967 13.3686 5.12807 13.3468 4.90947 13.306C4.69249 13.2658 4.4895 13.173 4.30105 13.0265C4.11261 12.88 3.97908 12.6882 3.89993 12.4518L3.8084 12.236C3.7481 12.0928 3.65226 11.9341 3.52196 11.7607C3.39112 11.5852 3.25921 11.4667 3.12568 11.4046L3.06107 11.3582C3.01908 11.3269 2.98031 11.29 2.94262 11.2464C2.90655 11.2028 2.87962 11.1592 2.86186 11.1156C2.84355 11.0708 2.85809 11.0356 2.90655 11.0077C2.955 10.9786 3.0433 10.9657 3.17037 10.9657L3.35343 10.9937C3.47404 11.0183 3.6248 11.0932 3.80409 11.2173C3.98339 11.3426 4.13091 11.5041 4.2456 11.7037C4.38559 11.9587 4.55411 12.1532 4.75171 12.2874C4.94931 12.4216 5.14853 12.4886 5.34936 12.4886C5.54857 12.4886 5.72248 12.4719 5.86839 12.4417C6.01377 12.4104 6.15107 12.3634 6.27759 12.3019C6.33359 11.8837 6.48219 11.5628 6.72448 11.3386C6.37882 11.3006 6.06761 11.2447 5.7914 11.1698C5.51465 11.0954 5.22929 10.9741 4.93531 10.8053C4.63918 10.6376 4.39528 10.4296 4.20091 10.1791C4.00708 9.93202 3.84717 9.60329 3.72333 9.20076C3.59949 8.79431 3.5365 8.32861 3.5365 7.79861C3.5365 7.04554 3.77556 6.40373 4.25583 5.87373C4.03077 5.30683 4.05123 4.67173 4.32044 3.9673C4.49542 3.91139 4.75656 3.95332 5.1033 4.09309C5.4495 4.23286 5.70256 4.35306 5.86355 4.45201C6.02507 4.55265 6.1543 4.63651 6.25121 4.70471C6.81494 4.54594 7.39751 4.46599 7.99946 4.46599C8.60142 4.46599 9.18399 4.54706 9.74933 4.70863L10.0955 4.48388C10.3324 4.33461 10.6124 4.19876 10.9344 4.07297C11.2574 3.94885 11.504 3.91475 11.6742 3.97066C11.9472 4.67508 11.9719 5.30963 11.7469 5.87708C12.2261 6.40597 12.4662 7.04778 12.4662 7.8014C12.4662 8.33029 12.4037 8.79935 12.2794 9.20747C12.1555 9.61559 11.994 9.94432 11.7959 10.1898C11.5988 10.4357 11.3533 10.6426 11.0582 10.8114C10.7637 10.9792 10.4767 11.1005 10.2021 11.1748C9.92539 11.2498 9.61526 11.3057 9.26744 11.3426C9.58349 11.6221 9.74125 12.0638 9.74125 12.6692V14.6411C9.74125 14.7529 9.77894 14.8457 9.8554 14.9206C9.93077 14.995 10.0508 15.0173 10.2151 14.986C11.6257 14.5052 12.7747 13.642 13.6647 12.3919C14.5547 11.1452 15 9.73802 15 8.17542C15 6.87391 14.6866 5.67358 14.061 4.57389C13.4348 3.47476 12.5857 2.60429 11.5143 1.96303L11.5127 1.96247Z"
    ></path>
  </svg>
);

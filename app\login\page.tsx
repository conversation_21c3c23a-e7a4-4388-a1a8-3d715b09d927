"use client";

import { useState, FormEvent } from "react";
import Link from "next/link";
import { But<PERSON> } from "../../components/button";
import { Container } from "../../components/container";
import { FormInput } from "../../components/form-input";
import { validateEmail, validatePassword } from "../../utils/validation";

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export default function LoginPage() {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.error;
    }

    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      newErrors.password = passwordValidation.error;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, show success state
      setIsSuccess(true);
      console.log("Login attempt:", formData);
    } catch (error) {
      setErrors({ general: "Invalid email or password. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <main className="bg-page-gradient pt-navigation-height">
        <Container className="flex min-h-[calc(100vh-var(--navigation-height))] items-center justify-center">
          <div className="w-full max-w-md text-center">
            <div className="rounded-lg border border-transparent-white bg-white bg-opacity-5 p-8">
              <div className="mb-4 text-4xl">✅</div>
              <h1 className="mb-2 text-2xl font-semibold text-off-white">Welcome back!</h1>
              <p className="text-primary-text">You have successfully signed in.</p>
            </div>
          </div>
        </Container>
      </main>
    );
  }

  return (
    <main className="bg-page-gradient pt-navigation-height">
      <Container className="flex min-h-[calc(100vh-var(--navigation-height))] items-center justify-center">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <h1 className="mb-2 text-4xl font-semibold text-off-white">Sign in to Linear</h1>
            <p className="text-primary-text">Welcome back! Please sign in to your account.</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.general && (
              <div className="rounded-lg border border-red-500 bg-red-500 bg-opacity-10 p-4">
                <p className="text-sm text-red-400">{errors.general}</p>
              </div>
            )}

            <FormInput
              label="Email"
              type="email"
              value={formData.email}
              onChange={handleInputChange("email")}
              error={errors.email}
              placeholder="Enter your email"
              required
              autoComplete="email"
            />

            <FormInput
              label="Password"
              type="password"
              value={formData.password}
              onChange={handleInputChange("password")}
              error={errors.password}
              placeholder="Enter your password"
              showPasswordToggle
              required
              autoComplete="current-password"
            />

            <Button
              type="submit"
              variant="primary"
              size="large"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Signing in...
                </span>
              ) : (
                "Sign In"
              )}
            </Button>

            <div className="text-center">
              <Link
                href="#"
                className="text-sm text-primary-text hover:text-off-white transition-colors"
              >
                Forgot your password?
              </Link>
            </div>

            <div className="text-center">
              <span className="text-sm text-primary-text">Don't have an account? </span>
              <Link
                href="/signup"
                className="text-sm text-off-white hover:text-primary-text transition-colors font-medium"
              >
                Sign up
              </Link>
            </div>
          </form>
        </div>
      </Container>
    </main>
  );
}

export const DescriptLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 180 56"
    fill="currentColor"
    role="img"
    focusable="false"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M81.969 27.838c.318-1.491 1.345-2.25 2.696-2.25 1.338 0 2.317.793 2.63 2.25h-5.326zm2.769-4.832c-3.438 0-5.577 2.654-5.577 5.901 0 3.363 2.285 5.857 5.682 5.857 2.342 0 3.763-1.047 4.642-2.365L87.3 31.05c-.55.726-1.43 1.11-2.372 1.11-1.543 0-2.576-.698-2.928-2.094h5.772l-.002.006 2.104.012c.08-.368.127-.747.127-1.176 0-2.828-1.803-5.901-5.262-5.901zM71.484 32.111c-1.899 0-3.04-1.361-3.04-3.04 0-1.68 1.163-3.041 3.04-3.041 1.876 0 3.04 1.361 3.04 3.04 0 1.68-1.142 3.041-3.04 3.041zm3.04-7.186c-.814-.96-1.969-1.548-3.402-1.548-3.205 0-5.51 2.55-5.51 5.694s2.284 5.693 5.51 5.693c1.433 0 2.588-.588 3.401-1.548v1.268h2.832V19.388h-2.832v5.537zM98.478 28.65c.791.54 1.293 1.353 1.293 2.424 0 2.166-1.49 3.668-4.278 3.668-2.623 0-4.03-1.316-4.59-2.645l2.408-1.376s.324 1.436 2.139 1.436c1.123 0 1.469-.486 1.469-.915 0-.2-.065-.565-.864-.875-1.606-.621-4.71-.9-4.71-3.734 0-2.32 1.857-3.557 4.148-3.557 2.142 0 3.518 1.305 4.04 2.32l-2.355 1.348s-.453-1.127-1.663-1.127-1.318.685-1.318.884c0 .58.604.818 1.901 1.193.768.222 1.557.424 2.382.953l-.002.002zM146 26.124h-2.442v4.596c0 1.436 1.426 1.127 2.442 1.127v2.585s-.535.088-1.469.088c-2.074 0-3.76-.619-3.76-3.8v-4.596h-1.88v-2.74h1.88v-3.093h2.787v3.093H146v2.74zm-39.3 5.833c1.157 0 2.046-.564 2.496-1.49l2.416 1.417c-.947 1.698-2.711 2.879-4.912 2.879-3.338 0-5.671-2.611-5.671-5.833 0-3.221 2.355-5.833 5.671-5.833 2.169 0 3.926 1.162 4.883 2.837l-2.411 1.45c-.458-.914-1.34-1.48-2.472-1.48-1.686 0-2.884 1.354-2.884 3.026 0 1.672 1.187 3.027 2.884 3.027zM132.21 32.447c-1.83 0-2.931-1.397-2.931-3.121s1.122-3.12 2.931-3.12 2.931 1.396 2.931 3.12c0 1.724-1.1 3.121-2.931 3.121zm.349-8.965c3.09 0 5.312 2.617 5.312 5.844 0 3.227-2.201 5.843-5.312 5.843-1.382 0-2.495-.603-3.28-1.588v5.705h-2.73V23.792h2.73v1.279c.785-.985 1.898-1.588 3.28-1.588zm-11.258 11.4V23.815h2.73v11.067h-2.73zm-5.087-9.177c.527-1.341 1.6-2.108 3.216-2.112v3.188c-1.895-.188-3.264.798-3.214 3.126l-.002-.005v4.98h-2.73V23.815h2.73v1.89zm5.087-3.367v-2.95h2.82v2.95h-2.82zM34 39.567c0 1.476.942 2.433 2.394 2.433h6.994c4.172 0 7.586-1.435 9.906-3.947H34v1.514zM43.388 15h-6.994C34.942 15 34 15.957 34 17.433v1.514h19.294C50.974 16.435 47.56 15 43.388 15zm6.104 17.347c0 1.194.765 1.969 1.945 1.969h4.19c.476-1.199.795-2.516.943-3.937h-5.133c-1.18 0-1.945.774-1.945 1.968zm-5.81-7.684c0 1.194.766 1.968 1.946 1.968H56.57a14.828 14.828 0 00-.942-3.937h-10c-1.18 0-1.945.775-1.945 1.969zm1.944 7.684c0-1.194-.765-1.968-1.945-1.968H34v3.937h9.681c1.18 0 1.945-.775 1.945-1.969zm-5.809-7.684c0-1.194-.765-1.969-1.945-1.969H34v3.937h3.872c1.18 0 1.945-.774 1.945-1.968z"
      fill="#fff"
    ></path>
  </svg>
);

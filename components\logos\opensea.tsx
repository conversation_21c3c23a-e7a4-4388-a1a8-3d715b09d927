export const OpenSeaLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 180 54"
    fill="currentColor"
    role="img"
    focusable="false"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M39.017 42c8.293 0 15.017-6.716 15.017-15 0-8.284-6.722-15-15.017-15C30.724 12 24 18.716 24 27c0 8.284 6.724 15 15.017 15zm9.805-12.395v-.967a.135.135 0 00-.172-.13l-4.568 1.32a.129.129 0 00-.063.038c-.49.54-.905.903-1.015.999l-.008.006c-.284.24-.636.372-1.006.372h-1.654v-1.685h1.314a.138.138 0 00.092-.034l.17-.155c.072-.067.158-.147.261-.25l.027-.026c.054-.055.112-.112.17-.176.067-.066.134-.14.195-.212.104-.111.203-.227.306-.349.074-.08.142-.17.208-.262a4.68 4.68 0 00.215-.276c.025-.037.052-.074.08-.112l.078-.113c.05-.074.1-.152.14-.226a4.3 4.3 0 00.442-.911l.013-.035a2.484 2.484 0 00.134-1.091 1.405 1.405 0 00-.031-.233v-.013a.937.937 0 00-.036-.164c-.055-.25-.142-.5-.251-.745a4.225 4.225 0 00-.122-.268 6.92 6.92 0 00-.32-.544c-.023-.037-.049-.075-.075-.113l-.059-.087c-.074-.115-.157-.225-.238-.332l-.055-.072a5.522 5.522 0 00-.153-.189l-.086-.104c-.08-.097-.159-.19-.239-.28a12.73 12.73 0 00-.856-.866 12.638 12.638 0 00-.846-.735l-.078-.065a11.86 11.86 0 01-.184-.139.14.14 0 00-.04-.02l-.176-.05v-1.454a.861.861 0 00-.248-.606.838.838 0 00-.6-.251.853.853 0 00-.85.857v.98l-.087-.025-.24-.068-.217-.06h-.002l-.004-.001h-.005l-1.65-.447a.086.086 0 00-.098.125l.264.487c.015.037.034.075.054.113l.037.076c.043.087.087.178.128.268.037.08.074.16.117.245l.055.123a16.104 16.104 0 01.88 2.425l.033.119.017.074c.05.195.092.39.123.585.025.134.048.262.06.392.019.146.037.293.044.439.012.134.018.274.018.408a4.338 4.338 0 01-.111 1.013l-.006.021c-.02.076-.041.155-.067.23-.022.08-.052.162-.083.246l-.032.09-.006.016c-.023.061-.046.123-.075.184a9.695 9.695 0 01-.538 1.075c-.287.507-.575.952-.778 1.239l-.036.053c-.014.02-.028.04-.04.06a.137.137 0 00.11.216h2.136v1.685h-2.16c-.58 0-1.117-.328-1.377-.854a1.486 1.486 0 01-.155-.837c.007-.085-.056-.165-.142-.165H30.44a.134.134 0 00-.134.134v.09a5.027 5.027 0 005.022 5.033h7.827c1.467 0 2.3-1.335 3.12-2.648.228-.366.455-.73.694-1.063.43-.598 1.466-1.073 1.768-1.202a.139.139 0 00.084-.126zm-17.348-2.203l-.065.102a.133.133 0 00.114.205h4.018a.133.133 0 00.11-.059c.053-.08.103-.163.147-.248.347-.582.656-1.213.769-1.678.264-1.131-.3-2.948-.952-4.41a.134.134 0 00-.235-.016l-3.906 6.104z"
      fill="#fff"
    ></path>
    <path
      d="M68.668 34.199c-1.358 0-2.606-.304-3.744-.912a7.077 7.077 0 01-2.694-2.536c-.658-1.097-.987-2.326-.987-3.686 0-1.361.329-2.583.987-3.667a7.077 7.077 0 012.694-2.536c1.138-.608 2.386-.912 3.744-.912 1.357 0 2.598.304 3.723.912a6.74 6.74 0 012.674 2.536c.658 1.084.987 2.306.987 3.667 0 1.36-.329 2.59-.987 3.686a6.924 6.924 0 01-2.674 2.536c-1.125.608-2.366.912-3.723.912zm0-3.092c1.152 0 2.07-.37 2.756-1.11.7-.74 1.05-1.717 1.05-2.932 0-1.23-.35-2.207-1.05-2.933-.686-.74-1.604-1.11-2.756-1.11-1.166 0-2.098.363-2.797 1.09-.686.726-1.029 1.71-1.029 2.953 0 1.228.343 2.213 1.029 2.952.699.727 1.631 1.09 2.797 1.09zM81.526 24.567c.343-.515.816-.93 1.42-1.248.603-.317 1.31-.476 2.118-.476.946 0 1.804.232 2.572.694.767.462 1.37 1.123 1.81 1.982.452.859.678 1.856.678 2.992 0 1.137-.226 2.14-.679 3.013-.438.858-1.042 1.526-1.81 2.001-.767.463-1.625.694-2.57.694-.796 0-1.502-.159-2.12-.476-.603-.317-1.076-.727-1.418-1.229v4.836h-3.518V23.002h3.517v1.566zm5.02 3.944c0-.845-.248-1.506-.741-1.982-.48-.488-1.077-.733-1.79-.733-.699 0-1.296.245-1.79.733-.48.49-.72 1.157-.72 2.002 0 .846.24 1.513.72 2.002a2.45 2.45 0 001.79.733c.7 0 1.296-.244 1.79-.733.493-.502.74-1.176.74-2.022zM102.789 28.353c0 .317-.021.647-.062.99h-7.96c.055.688.281 1.216.679 1.586.411.357.912.535 1.501.535.878 0 1.488-.357 1.831-1.07h3.743a4.805 4.805 0 01-1.049 1.962 5.083 5.083 0 01-1.871 1.367c-.755.33-1.598.496-2.53.496-1.125 0-2.126-.231-3.003-.694a5.033 5.033 0 01-2.057-1.982c-.494-.858-.74-1.863-.74-3.012 0-1.15.24-2.153.72-3.012a5.03 5.03 0 012.056-1.982c.878-.462 1.886-.694 3.024-.694 1.11 0 2.098.225 2.961.674a4.866 4.866 0 012.016 1.922c.494.833.741 1.804.741 2.914zm-3.6-.892c0-.581-.205-1.044-.617-1.387-.411-.344-.925-.516-1.542-.516-.59 0-1.09.166-1.502.496-.398.33-.645.8-.74 1.407h4.401zM111.638 22.883c1.344 0 2.413.423 3.209 1.268.809.833 1.213 1.982 1.213 3.449v6.46h-3.497v-6.005c0-.74-.198-1.314-.596-1.724-.398-.41-.932-.614-1.604-.614s-1.207.205-1.605.614c-.397.41-.596.985-.596 1.724v6.005h-3.517V23.002h3.517v1.466c.356-.488.836-.872 1.44-1.149.603-.29 1.282-.436 2.036-.436zM123.729 34.199c-1.056 0-2.002-.165-2.839-.495-.836-.33-1.508-.82-2.015-1.467-.494-.647-.755-1.427-.782-2.339h3.744c.054.516.239.912.555 1.19.316.264.727.396 1.234.396.521 0 .933-.112 1.234-.337.302-.238.453-.561.453-.971 0-.343-.123-.628-.37-.852a2.753 2.753 0 00-.885-.555 13.68 13.68 0 00-1.481-.495c-.932-.278-1.693-.555-2.283-.833a4.18 4.18 0 01-1.522-1.229c-.425-.541-.638-1.248-.638-2.12 0-1.295.487-2.305 1.461-3.032.973-.74 2.242-1.11 3.805-1.11 1.591 0 2.873.37 3.846 1.11.974.727 1.495 1.744 1.564 3.052h-3.806c-.027-.45-.199-.8-.514-1.05-.315-.265-.72-.397-1.214-.397-.425 0-.767.112-1.028.337-.26.211-.391.522-.391.931 0 .45.219.8.659 1.05.438.252 1.124.523 2.056.813.933.304 1.687.595 2.263.872.59.278 1.097.68 1.522 1.21.425.528.638 1.208.638 2.04 0 .793-.213 1.513-.638 2.16-.411.648-1.015 1.163-1.81 1.546-.795.383-1.735.575-2.818.575zM141.979 28.353c0 .317-.021.647-.062.99h-7.96c.055.688.281 1.216.678 1.586.412.357.912.535 1.502.535.878 0 1.488-.357 1.831-1.07h3.743a4.817 4.817 0 01-1.049 1.962 5.08 5.08 0 01-1.872 1.367c-.754.33-1.597.496-2.529.496-1.125 0-2.126-.231-3.004-.694a5.037 5.037 0 01-2.057-1.982c-.493-.858-.74-1.863-.74-3.012 0-1.15.24-2.153.72-3.012a5.025 5.025 0 012.057-1.982c.877-.462 1.885-.694 3.024-.694 1.11 0 2.097.225 2.961.674a4.872 4.872 0 012.016 1.922c.494.833.741 1.804.741 2.914zm-3.6-.892c0-.581-.206-1.044-.617-1.387-.412-.344-.926-.516-1.543-.516-.589 0-1.09.166-1.501.496-.398.33-.645.8-.741 1.407h4.402zM143.135 28.511c0-1.136.219-2.133.658-2.992.452-.859 1.063-1.52 1.831-1.982.768-.462 1.625-.694 2.571-.694.809 0 1.515.159 2.119.476.616.317 1.09.733 1.419 1.248v-1.565h3.517V34.06h-3.517v-1.565c-.343.515-.823.931-1.44 1.248-.604.317-1.31.476-2.119.476a4.85 4.85 0 01-2.55-.694c-.768-.475-1.379-1.143-1.831-2.001-.439-.872-.658-1.877-.658-3.013zm8.598.02c0-.845-.247-1.513-.741-2.002a2.382 2.382 0 00-1.769-.733c-.699 0-1.296.245-1.789.733-.48.476-.72 1.137-.72 1.982 0 .846.24 1.52.72 2.022.493.488 1.09.733 1.789.733.7 0 1.289-.244 1.769-.733.494-.49.741-1.156.741-2.002z"
      fill="#fff"
    ></path>
  </svg>
);

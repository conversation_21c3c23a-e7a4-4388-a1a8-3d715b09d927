export const MercuryLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 180 56"
    fill="currentColor"
    role="img"
    focusable="false"
  >
    <path
      d="M36.202 36.702a4.089 4.089 0 00-1.334-3.758 4.2 4.2 0 00-2.836-1.104c-.619 0-1.215-.14-1.763-.423a4.046 4.046 0 01-1.667-1.667c.214-.54.31-1.128.31-1.762 0 1.715 1.381 3.1 3.097 3.1h.071a3.1 3.1 0 003.05-3.1c0-.61.119-1.198.31-1.761a4.974 4.974 0 014.884-3.194 9.579 9.579 0 00-3.336-3.336v.235a5 5 0 01-1 2.983 3.595 3.595 0 00-.62-.423c.572-.728.858-1.62.858-2.536 0-.54-.096-1.08-.31-1.598a4.212 4.212 0 00-3.788-2.607h-.238c-1.049 0-2.073.141-3.074.423a12.234 12.234 0 00-8.649 8.643c-.714.87-1.143 1.926-1.167 3.054.072-7.07 5.79-12.8 12.89-12.871H32.223c7.052.117 12.747 5.801 12.818 12.87V28.153a4.83 4.83 0 01-1.167 3.054 4.867 4.867 0 01-2.621 1.62 4.956 4.956 0 01-1.167.141 5.026 5.026 0 01-2.979-.986c-.31.423-.69.775-1.096 1.104a4.874 4.874 0 01.977 3.218 9.579 9.579 0 003.336-3.336c.31-.023.62-.07.929-.14a10.39 10.39 0 01-9.22 5.566c-.74 0-1.43-.352-1.859-.916a2.41 2.41 0 01-.476-1.433c0-.704.333-1.362.834-1.785.476.118 1 .188 1.524.188a1.63 1.63 0 00-1.62 1.62c0 .893.715 1.621 1.62 1.621h.024a1.63 1.63 0 001.62-1.62c0-.893-.715-1.621-1.62-1.621a6.527 6.527 0 01-3.36-.94c-.214-.14-.428-.281-.619-.422a7.06 7.06 0 01-1.12-1.104c.191-.141.382-.305.572-.493a5.682 5.682 0 004.527 2.231 2.367 2.367 0 012.359 2.349c0 .516-.167 1.01-.477 1.432.787-.164 1.549-.399 2.264-.751l-.024-.047zm5.313-10.57a8.567 8.567 0 00-.762-2.278 4.233 4.233 0 00-3.217.799 4.23 4.23 0 00-1.668 3.359c0 .634-.143 1.22-.428 1.761a3.99 3.99 0 01-1.644 1.668c.643.235 1.215.61 1.715 1.08a5.437 5.437 0 001.453-1.668c.5-.869.763-1.831.763-2.841 0-.588.214-1.104.572-1.527a2.373 2.373 0 013.216-.352zm.167 1.81c-.024-.846-.739-1.527-1.62-1.551a1.61 1.61 0 00-1.62 1.597v.047c.023.87.738 1.55 1.62 1.55a1.61 1.61 0 001.62-1.597v-.047zm-3.383 1.596c-.167.634-.405 1.269-.763 1.832a4.136 4.136 0 003.217.799c.31-.047.619-.141.929-.259a4.214 4.214 0 002.62-3.78v-.212c0-1.034-.142-2.067-.428-3.077-1.12-4.18-4.432-7.492-8.625-8.643a5.034 5.034 0 011.62 2.607v.023a10.427 10.427 0 014.813 5.308c.5 1.198.762 2.513.762 3.9 0 .75-.357 1.432-.929 1.855a2.39 2.39 0 01-1.43.493 2.483 2.483 0 01-1.786-.846zM31.842 41c-7.052-.118-12.747-5.825-12.818-12.87v-.283a4.83 4.83 0 011.167-3.053 5.01 5.01 0 012.621-1.62c.381-.095.786-.142 1.168-.142 1.072 0 2.12.353 2.978.987.31-.423.69-.775 1.096-1.104a5.029 5.029 0 01-1.001-3.218 9.58 9.58 0 00-3.336 3.335c-.31.024-.619.07-.929.141a10.39 10.39 0 019.22-5.566c.74 0 1.43.352 1.859.916.31.4.477.892.477 1.433a2.33 2.33 0 01-.834 1.785c-.5-.118-1.001-.188-1.525-.188a1.63 1.63 0 001.62-1.62c0-.893-.715-1.622-1.62-1.622h-.024a1.63 1.63 0 00-1.62 1.621c0 .893.715 1.62 1.62 1.62 1.191 0 2.335.33 3.36.94.214.141.428.282.619.423.405.329.786.705 1.12 1.104-.191.14-.382.305-.572.493a5.682 5.682 0 00-4.527-2.231 2.44 2.44 0 01-1.525-.564 2.36 2.36 0 01-.834-1.785c0-.54.19-1.033.477-1.433-.786.165-1.573.423-2.287.752a4.089 4.089 0 001.334 3.758 4.2 4.2 0 002.835 1.104c.62 0 1.215.14 1.763.423a4.046 4.046 0 011.668 1.667 4.437 4.437 0 00-.31 1.762c0-1.715-1.382-3.1-3.097-3.1h-.024a3.1 3.1 0 00-3.05 3.1c0 .61-.119 1.198-.31 1.761a4.975 4.975 0 01-4.884 3.194 9.58 9.58 0 003.336 3.335 4.944 4.944 0 01.977-3.194c.19.165.405.306.62.423a4.084 4.084 0 00-.858 2.537c0 .54.095 1.08.31 1.597a4.212 4.212 0 003.788 2.607h.214c1.048 0 2.073-.141 3.073-.423a12.233 12.233 0 008.65-8.643c.714-.87 1.143-1.926 1.166-3.053-.071 7.07-5.79 12.8-12.889 12.87h-.286c.072.024.024.024.024.024zm-3.026-1.175a5.035 5.035 0 01-1.62-2.607v-.023a10.427 10.427 0 01-4.813-5.308 10.43 10.43 0 01-.762-3.899c0-.751.357-1.433.929-1.855a2.335 2.335 0 011.43-.494c.714 0 1.358.33 1.786.823.167-.658.405-1.269.763-1.832a4.137 4.137 0 00-3.217-.8 4.214 4.214 0 00-3.55 4.04v.236c0 1.033.143 2.066.43 3.076 1.143 4.204 4.43 7.516 8.624 8.643zm-5.504-7.656a4.232 4.232 0 003.217-.799 4.23 4.23 0 001.668-3.358c0-.634.143-1.222.428-1.762a3.99 3.99 0 011.644-1.668h-.023a5.032 5.032 0 01-1.716-1.08 5.44 5.44 0 00-1.453 1.668 5.65 5.65 0 00-.763 2.842c0 .587-.214 1.104-.571 1.526a2.373 2.373 0 01-1.787.822 2.336 2.336 0 01-1.43-.493c.19.799.453 1.574.786 2.302zm-.929-4.087c.048.846.739 1.55 1.62 1.55a1.61 1.61 0 001.62-1.597v-.047a1.602 1.602 0 00-1.62-1.55c-.905 0-1.62.728-1.62 1.62v.024zM55.5 21.2h3.574l3.336 8.972h.048l3.287-8.972h3.574v12.636h-2.263V23.69h-.024l-3.884 10.146h-1.596L57.716 23.69h-.024v10.146h-2.168L55.501 21.2zm20.061 0h8.482v1.973H77.85v3.218h5.885v1.879h-5.91v3.57h6.529v1.996h-8.815L75.56 21.2zm18.584 5.45c.334 0 .644-.024.977-.071.31-.047.596-.141.882-.258.262-.141.476-.33.643-.564.167-.235.262-.564.262-.94 0-.352-.071-.634-.238-.845-.167-.212-.358-.4-.596-.517a2.034 2.034 0 00-.834-.258 5.653 5.653 0 00-.905-.07h-2.073v3.546c.024-.024 1.882-.024 1.882-.024zM90 21.2h4.455c.596 0 1.191.07 1.763.188A4.683 4.683 0 0197.72 22c.429.282.81.681 1.048 1.127.263.47.405 1.034.405 1.691 0 .916-.262 1.668-.786 2.232-.524.587-1.239.963-2.144 1.174l3.407 5.59h-2.74l-2.954-5.355h-1.668v5.355H90V21.2zm24.039 2.96a2.85 2.85 0 00-1.31-.987 4.103 4.103 0 00-1.596-.305c-.62 0-1.239.117-1.811.352a4.47 4.47 0 00-1.406.987 4.066 4.066 0 00-.905 1.456c-.214.587-.334 1.198-.31 1.808 0 .681.096 1.316.31 1.88.214.563.5 1.056.882 1.48.381.398.833.727 1.358.962.524.235 1.119.329 1.786.329.691 0 1.287-.141 1.835-.4.5-.258.953-.61 1.287-1.056l1.834 1.268a5.569 5.569 0 01-2.073 1.62c-.81.376-1.787.588-2.883.588-1 0-1.929-.165-2.763-.494a6.487 6.487 0 01-2.168-1.362 5.999 5.999 0 01-1.406-2.114c-.334-.822-.5-1.714-.5-2.7 0-1.01.166-1.927.524-2.748a5.929 5.929 0 011.453-2.067 6.33 6.33 0 012.192-1.316 7.848 7.848 0 014.027-.352c.452.07.881.188 1.286.352.405.165.786.353 1.168.587.357.235.667.517.929.846 0 .047-1.74 1.386-1.74 1.386zm16.964 5.026a5.25 5.25 0 01-.405 2.067 4.195 4.195 0 01-1.096 1.55c-.476.423-1.024.775-1.62.986a6.057 6.057 0 01-2.049.353 6.16 6.16 0 01-2.049-.353 5.098 5.098 0 01-1.644-.986 4.738 4.738 0 01-1.096-1.55c-.262-.61-.381-1.292-.381-2.067V21.2h2.287v7.892c0 .376.048.728.143 1.104a2.8 2.8 0 00.477.963c.214.282.524.517.881.704.358.188.834.259 1.358.259.548 0 1.001-.094 1.358-.259.358-.164.644-.399.882-.704.214-.282.381-.61.476-.963.096-.352.143-.728.143-1.104V21.2h2.288c.047 0 .047 7.986.047 7.986zm10.269-2.537c.333 0 .643-.023.977-.07.333-.047.619-.141.881-.258.262-.141.477-.33.644-.564.166-.235.262-.564.262-.94 0-.352-.072-.634-.239-.845a1.578 1.578 0 00-.595-.517 2.033 2.033 0 00-.834-.258 5.654 5.654 0 00-.905-.07h-2.073v3.546c.024-.024 1.882-.024 1.882-.024zm-4.146-5.449h4.456c.595 0 1.191.07 1.763.188a4.686 4.686 0 011.501.611c.429.282.786.658 1.048 1.127.262.47.405 1.034.405 1.691 0 .916-.262 1.668-.786 2.232-.524.587-1.239.963-2.144 1.174l3.407 5.59h-2.74l-2.955-5.355h-1.667v5.355h-2.288V21.2zm17.774 7.164l-4.813-7.14h2.788l3.216 5.308 3.264-5.308H162l-4.813 7.14v5.472H154.9v-5.472z"
      fill="#fff"
    ></path>
  </svg>
);

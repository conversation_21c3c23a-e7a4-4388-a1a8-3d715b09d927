export const CashAppLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 180 54"
    fill="currentColor"
    role="img"
    focusable="false"
  >
    <path
      d="M77.53 23.818a.528.528 0 01-.697-.137 4.043 4.043 0 00-3.437-1.729c-2.619 0-4.226 2.069-4.226 5.022 0 2.954 1.636 5.062 4.255 5.062a4.007 4.007 0 003.444-1.807.52.52 0 01.687-.154l1.244.72a.528.528 0 01.193.777 6.468 6.468 0 01-5.564 2.872c-4.187 0-7.025-3.032-7.025-7.453s2.838-7.414 6.962-7.414a6.606 6.606 0 015.539 2.738.532.532 0 01-.144.765l-1.23.738zM86.683 33.642v-.6c-.655.842-1.568 1.365-3.038 1.365-2.174 0-3.683-1.186-3.683-3.267 0-2.591 2.315-3.036 3.742-3.235 1.532-.222 2.838-.327 2.838-1.327 0-.885-1.025-1.124-1.892-1.124a6.44 6.44 0 00-2.858.722.52.52 0 01-.713-.225l-.449-.935a.526.526 0 01.216-.692 8.608 8.608 0 014.02-1.007c2.494 0 4.255 1.023 4.255 3.497v6.828a.522.522 0 01-.523.523h-1.369a.524.524 0 01-.546-.523zm-.141-4.721c-.481.363-1.368.484-2.314.653-.947.17-1.732.422-1.732 1.448 0 .944.684 1.385 1.673 1.385 1.227 0 2.373-.683 2.373-2.27V28.92zM91.37 31.46a.524.524 0 01.724-.068 4.875 4.875 0 003.044.98c1.13 0 1.964-.362 1.964-1.244 0-.804-.766-.964-2.619-1.288-1.964-.327-3.843-.98-3.843-3.215 0-2.33 2.014-3.313 4.308-3.313a6.454 6.454 0 013.99 1.307.52.52 0 01.072.755l-.654.767a.527.527 0 01-.72.082 4.623 4.623 0 00-2.783-.889c-.965 0-1.81.281-1.81 1.043 0 .846 1.185 1.006 2.373 1.209 2.537.44 4.148 1.163 4.148 3.267 0 2.209-1.771 3.575-4.583 3.575a6.612 6.612 0 01-4.256-1.386.53.53 0 01-.068-.751l.713-.83zM101.119 33.644V20.345a.523.523 0 01.524-.523h1.509a.523.523 0 01.524.523v4.542a3.936 3.936 0 013.241-1.568c2.232 0 3.541 1.587 3.541 3.855v6.47a.523.523 0 01-.523.522h-1.509a.524.524 0 01-.524-.522v-5.882c0-1.127-.364-2.15-1.811-2.15-1.371 0-2.415.964-2.415 2.572v5.463a.524.524 0 01-.524.523h-1.509a.52.52 0 01-.524-.526zM127.064 33.815l-1.086-3.202h-5.494l-1.089 3.202a.525.525 0 01-.495.353h-1.771a.527.527 0 01-.519-.456.52.52 0 01.029-.247l4.945-13.301a.53.53 0 01.492-.327h2.419a.53.53 0 01.494.343l4.927 13.298a.517.517 0 01-.252.644.525.525 0 01-.243.059h-1.869a.525.525 0 01-.488-.366zm-3.804-11.661l-2.115 6.227h4.148l-2.033-6.227zM133.709 24.082v.824a3.857 3.857 0 013.274-1.588c2.897 0 4.769 2.392 4.769 5.525 0 3.134-1.872 5.555-4.769 5.555a3.844 3.844 0 01-3.274-1.608v4.336a.523.523 0 01-.523.523h-1.509a.523.523 0 01-.524-.523v-13.07a.523.523 0 01.524-.523h1.509a.52.52 0 01.491.341.509.509 0 01.032.209zm2.737 8.097c1.771 0 2.619-1.51 2.619-3.336 0-1.826-.864-3.297-2.619-3.297-1.754 0-2.737 1.467-2.737 3.297s.934 3.336 2.737 3.336zM145.887 24.082v.824a3.849 3.849 0 013.274-1.588c2.9 0 4.772 2.391 4.772 5.525 0 3.134-1.872 5.555-4.772 5.555a3.85 3.85 0 01-3.274-1.608v4.336a.522.522 0 01-.523.523h-1.509a.523.523 0 01-.524-.523v-13.07a.523.523 0 01.524-.523h1.509a.52.52 0 01.523.55zm2.74 8.097c1.771 0 2.619-1.51 2.619-3.336 0-1.826-.865-3.297-2.619-3.297-1.754 0-2.74 1.467-2.74 3.297s.946 3.336 2.74 3.336z"
      fill="#fff"
    ></path>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M33.466 13C29.895 13 27 15.89 27 19.454v15.092C27 38.111 29.895 41 33.466 41h14.97c3.57 0 6.465-2.89 6.465-6.454V19.454c0-3.565-2.894-6.454-6.465-6.454h-14.97zm12.018 10.414a5.918 5.918 0 00-3.81-1.396c-1.15 0-2.299.38-2.299 1.435 0 .963 1.024 1.34 2.284 1.803l.373.138c2.493.84 4.546 1.869 4.546 4.308 0 2.649-2.06 4.458-5.43 4.665l-.305 1.42a.575.575 0 01-.565.452H38.16a.586.586 0 01-.558-.702l.328-1.498a7.791 7.791 0 01-3.4-1.859.567.567 0 010-.816l1.176-1.147a.572.572 0 01.802 0 5.675 5.675 0 004.003 1.564c1.534 0 2.575-.65 2.575-1.679 0-.93-.853-1.242-2.473-1.834-.172-.063-.353-.128-.542-.199-2.082-.74-4.053-1.8-4.053-4.261 0-2.85 2.385-4.24 5.196-4.377l.296-1.446a.575.575 0 01.562-.449h2.111a.572.572 0 01.562.685l-.328 1.616a8.705 8.705 0 012.932 ********** 0 01.03.823l-1.097 1.098a.582.582 0 01-.798.016z"
      fill="#fff"
    ></path>
  </svg>
);

# linear-server

How i started

    - npm init -y
    - npm i -D typescript ts-node nodemon @types/node @types/express
    - tsc --init or past same tsconfig.json; - then setup package.json

"scripts": {
"start": "node dist/app.js",
"dev": "nodemon src/app.ts",
"build": "tsc -p ."
},

### [linear-client Repo](https://github.com/yashrajillusion/linear-client.git)

## Home Screen

<img width="1440" alt="image" src="https://user-images.githubusercontent.com/95868808/224548458-9d0c8be6-84a8-4e6a-ada8-4947df386f28.png">

## Create Ticket Modal

<img width="1440" alt="image" src="https://user-images.githubusercontent.com/95868808/224548507-efad02c4-3ee9-4ccc-8572-4c4a5c51bb46.png">


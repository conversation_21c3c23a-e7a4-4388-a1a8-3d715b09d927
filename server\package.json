{"name": "linear-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc -p ."}, "repository": {"type": "git", "url": "git+https://github.com/yashrajillusion/linear-server.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/yashrajillusion/linear-server/issues"}, "homepage": "https://github.com/yashrajillusion/linear-server#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "mongoose": "^6.10.0", "socket.io": "^4.6.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^18.14.1", "nodemon": "^2.0.20", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}
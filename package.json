{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@vercel/analytics": "^0.1.5", "class-variance-authority": "^0.3.0", "classnames": "^2.3.2", "js-cookie": "^3.0.1", "next": "^13.1.6", "react": "18.2.0", "react-dom": "18.2.0", "react-intersection-observer": "^9.4.1"}, "devDependencies": {"@types/js-cookie": "^3.0.2", "@types/node": "18.11.3", "@types/react": "18.0.21", "@types/react-dom": "18.0.6", "autoprefixer": "^10.4.12", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-tailwindcss": "^0.2.2", "tailwindcss": "^3.2.6", "typescript": "^4.9.5"}}
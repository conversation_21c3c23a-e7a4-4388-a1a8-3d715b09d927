# Rebuilding Linear.app's homepage with Next.js and Tailwind

**NOTE:** I am NOT affiliated with [Linear.app](https://linear.app/), but just really like their website, and think rebuilding this with Tailwind can teach a lot to people. All of the images in `/public/img` are owned by Linear, they are only used as teaching material here.

## Work in progress 🚧

This version is the end result of the first video. Any next part will also be committed in main, and extend this current version.

## See what we build so far

[Visit the preview of what we build here.](https://rebuilding-linear.vercel.app/)

## Video links 📺

* [Part One](https://youtu.be/ls_b-1a0ZUc): Setting up the project, building the header, main navigation, part of the hero component and the footer.
* [Part Two](https://youtu.be/R5PjNcIdAzU): Building the super detailed animated hero on the homepage.
* [Part Three](https://youtu.be/ar_NCPlRt_U): Building the client logo's and USP section.
* [Part Four](https://youtu.be/RoQ0MXiWQoY): Building the interactive shortcuts keyboard.
* [Part Five](https://youtu.be/lRtiKHy5Db8): Building the command menu.
* [Part Six](https://youtu.be/21kgdgWiNDs): Building feature blocks with awesome gradients.

## Running this project

Run `yarn` or `npm i` to install the project. Next run `yarn dev` or `npm run dev` to view the project by clicking the link in the terminal.

## Design remains copyrighted by Linear, it can only be used for educational purposes.

See the [LICENSE](/LICENSE.md) for more information about reusing this code.